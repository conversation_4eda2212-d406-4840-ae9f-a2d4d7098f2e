<script lang="ts">
  import { onMount } from 'svelte';

  let wsConnection: WebSocket | null = null;
  let connectionStatus = 'disconnected';
  let messages: Array<{
    sender: 'therapist' | 'patient';
    content: string;
    timestamp: string;
    thinking?: string;
    metadata?: any;
  }> = [];
  let therapistThoughts: Array<{ content: string; timestamp: string }> = [];
  let patientThoughts: Array<{ content: string; timestamp: string }> = [];

  // Configuration state
  let maxTurns = 20;
  let conversationActive = false;
  let conversationId: string | null = null;
  
  onMount(() => {
    connectWebSocket();
    
    return () => {
      if (wsConnection) {
        wsConnection.close();
      }
    };
  });
  
  function connectWebSocket() {
    const wsUrl = import.meta.env.VITE_WS_URL || 'ws://localhost:3000';
    wsConnection = new WebSocket(`${wsUrl}/ws`);
    
    wsConnection.onopen = () => {
      connectionStatus = 'connected';
      console.log('🔌 WebSocket connected successfully');
    };

    wsConnection.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        console.log('📨 Received WebSocket message:', data);

        handleWebSocketMessage(data);
      } catch (error) {
        console.error('❌ Error parsing WebSocket message:', error);
      }
    };
    
    wsConnection.onclose = () => {
      connectionStatus = 'disconnected';
      console.log('WebSocket disconnected');
    };
    
    wsConnection.onerror = (error) => {
      connectionStatus = 'error';
      console.error('WebSocket error:', error);
    };
  }
  
  function handleWebSocketMessage(data: any) {
    console.log(`🔄 Handling message type: ${data.type}`);

    switch (data.type) {
      case 'welcome':
        console.log('👋 Welcome message received');
        break;

      case 'conversation_created':
        console.log('✅ Conversation created:', data.data);
        conversationId = data.data.conversationId;
        break;

      case 'conversation_started':
        console.log('🚀 Conversation started:', data.data);
        break;

      case 'conversation_message':
        console.log('💬 Conversation message received:', data.data);
        handleConversationMessage(data.data);
        break;

      case 'conversation_ended':
        console.log('🏁 Conversation ended:', data.data);
        conversationActive = false;
        break;

      case 'conversation_paused':
        console.log('⏸️ Conversation paused');
        break;

      case 'conversation_resumed':
        console.log('▶️ Conversation resumed');
        break;

      case 'conversation_cleared':
        console.log('🗑️ Conversation cleared');
        messages = [];
        therapistThoughts = [];
        patientThoughts = [];
        break;

      case 'error':
        console.error('❌ Server error:', data.message);
        break;

      default:
        console.warn('⚠️ Unknown message type:', data.type);
        // For backward compatibility, treat unknown messages as general messages
        if (data.message) {
          messages = [...messages, {
            sender: 'therapist', // Default sender
            content: data.message,
            timestamp: data.timestamp || new Date().toISOString()
          }];
        }
    }
  }

  function handleConversationMessage(messageData: any) {
    console.log('📝 Processing conversation message:', messageData);

    if (messageData.message) {
      // Add conversation message
      messages = [...messages, {
        sender: messageData.message.sender,
        content: messageData.message.content,
        timestamp: messageData.message.timestamp,
        thinking: messageData.thinking?.content,
        metadata: messageData.metadata
      }];

      console.log(`💭 ${messageData.message.sender} said: "${messageData.message.content}"`);
    }

    if (messageData.thinking) {
      // Add thinking to appropriate array
      const thought = {
        content: messageData.thinking.content,
        timestamp: messageData.thinking.timestamp
      };

      if (messageData.thinking.agent === 'therapist') {
        therapistThoughts = [...therapistThoughts, thought];
        console.log(`🧠 Therapist thinking: "${thought.content}"`);
      } else if (messageData.thinking.agent === 'patient') {
        patientThoughts = [...patientThoughts, thought];
        console.log(`💭 Patient thinking: "${thought.content}"`);
      }
    }
  }

  function startConversation() {
    console.log('🚀 Starting conversation...');
    console.log(`⚙️ Configuration: maxTurns=${maxTurns}`);

    if (wsConnection && wsConnection.readyState === WebSocket.OPEN) {
      conversationActive = true;

      const message = {
        type: 'start_conversation',
        config: { maxTurns }
      };

      console.log('📤 Sending start conversation message:', message);
      wsConnection.send(JSON.stringify(message));
    } else {
      console.error('❌ WebSocket not connected');
    }
  }

  function clearConversation() {
    console.log('🗑️ Clearing conversation...');

    if (wsConnection && wsConnection.readyState === WebSocket.OPEN && conversationId) {
      wsConnection.send(JSON.stringify({
        type: 'clear_conversation'
      }));
    }

    // Clear local state
    messages = [];
    therapistThoughts = [];
    patientThoughts = [];
    conversationActive = false;
    conversationId = null;
  }
</script>

<svelte:head>
  <title>MiCA - AI Therapy Simulation</title>
</svelte:head>

<div class="min-h-screen bg-neutral-50">
  <!-- Header -->
  <header class="bg-white shadow-sm border-b border-neutral-200">
    <div class="mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between items-center h-16">
        <div class="flex items-center">
          <h1 class="text-2xl font-bold text-neutral-900">MiCA</h1>
          <span class="ml-2 text-sm text-neutral-500">AI Therapy Simulation</span>
        </div>
        <div class="flex items-center space-x-2">
          <div class="flex items-center space-x-2">
            <span class="text-sm text-neutral-600">Status:</span>
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {
              connectionStatus === 'connected' ? 'bg-green-100 text-green-800' :
              connectionStatus === 'error' ? 'bg-red-100 text-red-800' :
              'bg-yellow-100 text-yellow-800'
            }">
              {connectionStatus}
            </span>
          </div>
        </div>
      </div>
    </div>
  </header>

  <!-- Configuration Panel -->
  <div class="bg-white border-b border-neutral-200">
    <div class="mx-auto px-4 sm:px-6 lg:px-8 py-4">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-6">
          <div class="flex items-center space-x-2">
            <label for="maxTurns" class="label">Max Turns:</label>
            <input
              id="maxTurns"
              type="number"
              bind:value={maxTurns}
              min="1"
              max="100"
              class="input w-20"
              disabled={conversationActive}
            />
          </div>
        </div>
        <div class="flex items-center space-x-3">
          <button
            on:click={startConversation}
            disabled={conversationActive || connectionStatus !== 'connected'}
            class="btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {conversationActive ? 'Conversation Active' : 'Start Conversation'}
          </button>
          <button
            on:click={clearConversation}
            class="btn-secondary"
          >
            Clear
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Main Content - Three Panes -->
  <div class="mx-auto px-4 sm:px-6 lg:px-8 py-6">
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 h-[calc(100vh-200px)]">
      
      <!-- Left Pane: Therapist Thinking -->
      <div class="card p-6">
        <h2 class="text-lg font-semibold text-neutral-900 mb-4 flex items-center">
          <div class="w-3 h-3 bg-primary-500 rounded-full mr-2"></div>
          Therapist Thinking
        </h2>
        <div class="space-y-3 h-full overflow-y-auto">
          {#each therapistThoughts as thought}
            <div class="thinking-therapist bg-primary-50 p-3 rounded-lg">
              <p class="text-sm text-neutral-800">{thought.content}</p>
              <div class="mt-2 text-xs text-primary-600">
                <span class="font-medium">Time:</span> {new Date(thought.timestamp).toLocaleTimeString()}
              </div>
            </div>
          {/each}

          {#if therapistThoughts.length === 0}
            <div class="flex items-center justify-center h-full text-neutral-500">
              <p class="text-sm">Therapist thinking will appear here during conversation...</p>
            </div>
          {/if}
        </div>
      </div>

      <!-- Middle Pane: Conversation -->
      <div class="card p-6">
        <h2 class="text-lg font-semibold text-neutral-900 mb-4 flex items-center">
          <div class="w-3 h-3 bg-accent-500 rounded-full mr-2"></div>
          Conversation
        </h2>
        <div class="space-y-4 h-full overflow-y-auto">
          {#each messages as message}
            <div class="flex flex-col space-y-1">
              <div class="text-xs text-neutral-500 flex items-center space-x-2">
                <span class="font-medium capitalize {message.sender === 'therapist' ? 'text-primary-600' : 'text-secondary-600'}">
                  {message.sender === 'therapist' ? '👩‍⚕️ Dr. Chen' : '👤 Alex'}
                </span>
                <span>•</span>
                <span>{new Date(message.timestamp).toLocaleTimeString()}</span>
                {#if message.metadata}
                  <span>•</span>
                  <span class="text-xs px-2 py-1 rounded-full {
                    message.metadata.sentiment === 'positive' ? 'bg-green-100 text-green-700' :
                    message.metadata.sentiment === 'negative' ? 'bg-red-100 text-red-700' :
                    'bg-gray-100 text-gray-700'
                  }">
                    {message.metadata.sentiment}
                  </span>
                {/if}
              </div>
              <div class="message-bubble {message.sender === 'therapist' ? 'bg-primary-50 border-l-4 border-primary-500' : 'bg-secondary-50 border-l-4 border-secondary-500'} text-neutral-900 p-3 rounded-r-lg">
                {message.content}
              </div>
            </div>
          {/each}

          {#if messages.length === 0}
            <div class="flex items-center justify-center h-full text-neutral-500">
              <p>No conversation yet. Click "Start Conversation" to begin.</p>
            </div>
          {/if}
        </div>
      </div>

      <!-- Right Pane: Patient Thinking -->
      <div class="card p-6">
        <h2 class="text-lg font-semibold text-neutral-900 mb-4 flex items-center">
          <div class="w-3 h-3 bg-secondary-500 rounded-full mr-2"></div>
          Patient Thinking
        </h2>
        <div class="space-y-3 h-full overflow-y-auto">
          {#each patientThoughts as thought}
            <div class="thinking-patient bg-secondary-50 p-3 rounded-lg">
              <p class="text-sm text-neutral-800">{thought.content}</p>
              <div class="mt-2 text-xs text-secondary-600">
                <span class="font-medium">Time:</span> {new Date(thought.timestamp).toLocaleTimeString()}
              </div>
            </div>
          {/each}

          {#if patientThoughts.length === 0}
            <div class="flex items-center justify-center h-full text-neutral-500">
              <p class="text-sm">Patient thinking will appear here during conversation...</p>
            </div>
          {/if}
        </div>
      </div>
      
    </div>
  </div>
</div>
